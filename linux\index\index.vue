<template>
	<page-container ref="pageContainer" :isShowNav="false" bgColorPage="#FAFAFA" @scroll="onPageScrollHandler">
		<image
			:src="backgroundImage"
			:style="backgroundImageStyle"
			mode="aspectFill"
			class="w-100% h-360 fixed top-0 z-10"
		/>
		<custom-nav bg-color="unset" title="" :is-back="true"> </custom-nav>
		<view class="content-wrapper px-24">
			<view class="server-info sticky top-200 z-10">
				<view class="relative flex justify-between">
					<view class="flex flex-col">
						<view class="text-36 font-bold">{{ serverDisplayInfo.name }}</view>
						<view class="text-24 py-16"
							>IP：{{ serverDisplayInfo.ip }} | {{ serverDisplayInfo.system }}</view
						>
						<view class="text-24 flex items-center">
							<view class="w-16 h-16 rd-50%" :style="{ backgroundColor: serverStatusColor }"></view>
							<text class="text-24 pl-16" :style="{ color: serverStatusColor }">{{
								serverDisplayInfo.uptime
							}}</text>
						</view>
					</view>
					<image
						src="@/static/index/server-bg.png"
						mode="aspectFit"
						class="absolute -top-130 -right-80 w-460 h-460"
						:style="serverBgStyle"
					></image>
				</view>
			</view>
			<view class="detail mt-68 pb-48 z-1">
				<function-list
					title="功能"
					:function-list="basicFunctionList"
					:show-edit="true"
					:columns="5"
					@itemClick="handleBasicFunctionClick"
					@editClick="() => handleFunctionListEdit('basic')"
				/>
				<function-list
					class="mt-24"
					title="插件"
					:function-list="pluginFunctionList"
					:show-edit="true"
					:columns="4"
					@itemClick="handlePluginFunctionClick"
					@editClick="() => handleFunctionListEdit('plugin')"
				/>
				<function-list
					class="my-24"
					title="环境"
					:function-list="environmentFunctionList"
					:show-edit="true"
					:columns="4"
					@itemClick="handleEnvironmentFunctionClick"
					@editClick="() => handleFunctionListEdit('environment')"
				/>
				<statusInfo title="负载" expanded-text="收起" collapsed-text="详情">
					<template #basic>
						<view class="flex items-center justify-between">
							<view class="flex items-center justify-between w-60%">
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">1分钟</text>
									<text class="text-24 font-800">{{ formatLoadValue(loadData.one) }}</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">5分钟</text>
									<text class="text-24 font-800">{{ formatLoadValue(loadData.five) }}</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">15分钟</text>
									<text class="text-24 font-800">{{ formatLoadValue(loadData.fifteen) }}</text>
								</view>
							</view>
							<view class="">
								<MetricProgressBar
									mode="vertical"
									:value="loadData.percentage"
									height="184rpx"
									thickness="100rpx"
									:server-status="serverDisplayInfo.isOnline"
									:animated="true"
								/>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<!-- 监控已开启时显示图表 -->
							<view v-if="isMonitoringEnabled">
								<ECharts
									canvas-id="load-chart-info"
									chart-type="line"
									:chart-data="loadChartData"
									:opts="getLoadChartStyle()"
									:height="500"
								/>
							</view>

							<!-- 监控未开启时显示空状态 -->
							<view
								v-else
								class="empty-state-container flex flex-col items-center justify-center text-center"
							>
								<text class="text-28 font-600 color-#333 mb-8">服务器监控未开启</text>
								<text class="text-24 color-#999 mb-24">开启监控后可查看负载详细数据</text>
								<uv-button
									type="primary"
									size="mini"
									:custom-style="{ backgroundColor: '#20a50a', borderColor: '#20a50a' }"
									@click="navigateToMonitorPage"
								>
									去开启监控
								</uv-button>
							</view>
						</view>
					</template>
				</statusInfo>
				<statusInfo class="mt-24" title="CPU" expanded-text="收起" collapsed-text="详情">
					<template #basic>
						<view class="flex items-center justify-between">
							<view class="flex items-center justify-between w-60%">
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">核心</text>
									<text class="text-24 font-800">{{
										cpuDetailInfo.isDataValid ? cpuDetailInfo.logicalCores : 0
									}}</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">已用</text>
									<text class="text-24 font-800">{{
										formatCpuValue(cpuDetailInfo.isDataValid ? cpuDetailInfo.usage : 0)
									}}</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">空闲</text>
									<text class="text-24 font-800">{{
										formatCpuValue(cpuDetailInfo.isDataValid ? cpuDetailInfo.idle : 0)
									}}</text>
								</view>
							</view>
							<view class="">
								<ECharts
									canvas-id="cpu-chart"
									chart-type="gauge"
									:chart-data="getBaseChartConfig(cpuDetailInfo.usage, 'CPU')"
									:height="140"
								/>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<!-- 监控已开启时显示详细信息 -->
							<view v-if="isMonitoringEnabled">
								<view class="flex flex-col mb-24 bg-#F7F7F7 p-24 rd-16">
									<text class="text-28 font-800">基础信息</text>
									<text class="text-24 text-secondary py-8">{{
										cpuDetailInfo.isDataValid && cpuDetailInfo.modelName
											? cpuDetailInfo.modelName
											: 'CPU 型号未知'
									}}</text>
									<text class="text-24 text-secondary">
										{{ cpuDetailInfo.isDataValid ? cpuDetailInfo.physicalCpuCount : 0 }}个物理CPU，
										{{ cpuDetailInfo.isDataValid ? cpuDetailInfo.physicalCores : 0 }}个物理核心，
										{{ cpuDetailInfo.isDataValid ? cpuDetailInfo.logicalCores : 0 }}个逻辑核心
									</text>
									<!-- 显示各核心使用率信息 -->
									<view
										v-if="
											cpuDetailInfo.isDataValid &&
											cpuDetailInfo.coreUsages &&
											cpuDetailInfo.coreUsages.length > 0
										"
										class="mt-16"
									>
										<text class="text-24 text-secondary">各核心使用率：</text>
										<view class="flex flex-wrap mt-8">
											<text
												v-for="(usage, index) in cpuDetailInfo.coreUsages"
												:key="index"
												class="text-20 text-secondary mr-16 mb-8"
											>
												核心{{ index + 1 }}: {{ usage >= 0 ? usage + '%' : '未知' }}
											</text>
										</view>
									</view>
									<!-- 数据无效时的提示 -->
									<view v-if="!cpuDetailInfo.isDataValid" class="mt-16">
										<text class="text-20 text-#999">CPU 数据解析异常，显示信息可能不准确</text>
									</view>
								</view>
								<ECharts
									canvas-id="cpu-chart-info"
									chart-type="line"
									:chart-data="cpuChartData"
									:opts="getLoadChartStyle()"
									:height="500"
								/>
							</view>

							<!-- 监控未开启时显示空状态 -->
							<view
								v-else
								class="empty-state-container flex flex-col items-center justify-center text-center"
							>
								<text class="text-28 font-600 color-#333 mb-8">服务器监控未开启</text>
								<text class="text-24 color-#999 mb-24">开启监控后可查看CPU详细数据</text>
								<uv-button
									type="primary"
									size="mini"
									:custom-style="{ backgroundColor: '#20a50a', borderColor: '#20a50a' }"
									@click="navigateToMonitorPage"
								>
									去开启监控
								</uv-button>
							</view>
						</view>
					</template>
				</statusInfo>
				<statusInfo class="mt-24" title="内存" expanded-text="收起" collapsed-text="详情">
					<template #basic>
						<view class="flex items-center justify-between">
							<view class="flex items-center justify-between w-60%">
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">总计</text>
									<text class="text-24 font-800">{{
										memDetailInfo.isDataValid ? memDetailInfo.memNewTotal : '0MB'
									}}</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">已用</text>
									<text class="text-24 font-800">{{
										formatMemoryValue(memDetailInfo.isDataValid ? memDetailInfo.memRealUsed : 0)
									}}</text>
								</view>
								<view class="flex flex-col items-center">
									<text class="text-24 mb-16 text-secondary">可用</text>
									<text class="text-24 font-800">{{
										formatMemoryValue(memDetailInfo.isDataValid ? memDetailInfo.memAvailable : 0)
									}}</text>
								</view>
							</view>
							<view class="">
								<ECharts
									canvas-id="memory-chart"
									chart-type="gauge"
									:chart-data="getBaseChartConfig(memDetailInfo.memUsagePercentage, '内存')"
									:height="140"
								/>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<!-- 监控已开启时显示详细信息 -->
							<view v-if="isMonitoringEnabled">
								<ECharts
									canvas-id="memory-chart-info"
									chart-type="line"
									:chart-data="memoryChartData"
									:opts="getLoadChartStyle()"
									:height="500"
								/>
							</view>

							<!-- 监控未开启时显示空状态 -->
							<view
								v-else
								class="empty-state-container flex flex-col items-center justify-center text-center"
							>
								<text class="text-28 font-600 color-#333 mb-8">服务器监控未开启</text>
								<text class="text-24 color-#999 mb-24">开启监控后可查看内存详细数据</text>
								<uv-button
									type="primary"
									size="mini"
									:custom-style="{ backgroundColor: '#20a50a', borderColor: '#20a50a' }"
									@click="navigateToMonitorPage"
								>
									去开启监控
								</uv-button>
							</view>
						</view>
					</template>
				</statusInfo>

				<statusInfo class="mt-24" title="磁盘" expanded-text="收起" collapsed-text="详情">
					<template #desc>
						<text class="text-24 text-secondary"> 若有多个磁盘，可左右滑动查看 </text>
					</template>
					<template #basic>
						<swiper class="server-item h-100">
							<swiper-item>
								<view class="flex items-center justify-between">
									<view class="flex items-center justify-between w-60%">
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">总计</text>
											<text class="text-24 font-800">100GB</text>
										</view>
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">已用</text>
											<text class="text-24 font-800">90GB</text>
										</view>
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">空闲</text>
											<text class="text-24 font-800">10GB</text>
										</view>
									</view>
									<view class="w-40% ml-80 mt-24">
										<MetricProgressBar
											class="z-1"
											mode="horizontal"
											:value="90"
											width="100%"
											thickness="16rpx"
											:server-status="true"
											:animated="true"
											text-position="top-right"
										/>
									</view>
								</view>
							</swiper-item>
							<swiper-item>
								<view class="flex items-center justify-between">
									<view class="flex items-center justify-between w-60%">
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">总计</text>
											<text class="text-24 font-800">50GB</text>
										</view>
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">已用</text>
											<text class="text-24 font-800">25GB</text>
										</view>
										<view class="flex flex-col items-center">
											<text class="text-24 mb-16 text-secondary">空闲</text>
											<text class="text-24 font-800">25GB</text>
										</view>
									</view>
									<view class="w-40% ml-80 mt-24">
										<MetricProgressBar
											mode="horizontal"
											:value="66"
											width="100%"
											thickness="16rpx"
											:server-status="true"
											:animated="true"
											text-position="top-right"
										/>
									</view>
								</view>
							</swiper-item>
						</swiper>
					</template>

					<template #details>
						<view class="details-info">
							<!-- 监控已开启时显示详细信息 -->
							<view v-if="isMonitoringEnabled">
								<view class="flex flex-col mb-24 bg-#F7F7F7 p-24 rd-16">
									<text class="text-28 font-800">基础信息</text>
									<view class="flex items-center py-8">
										<text class="text-24 text-secondary flex-1"> 挂载点：/(/) </text>
										<text class="text-24 text-secondary flex-1"> 文件系统: /dev/vda1 </text>
									</view>
									<text class="text-24 text-secondary"> 类型: xfs ，系统占用: 56.21% </text>
								</view>
								<view class="flex flex-col mb-24 bg-#F7F7F7 p-24 rd-16">
									<text class="text-28 font-800">Inode信息</text>
									<view class="flex items-center py-8">
										<text class="text-24 text-secondary flex-1"> 总数：411155 </text>
										<text class="text-24 text-secondary flex-1"> 已用: 5712545 </text>
									</view>
									<view class="flex items-center">
										<text class="text-24 text-secondary flex-1"> 可用: 55544555665 </text>
										<text class="text-24 text-secondary flex-1"> 使用率: 1.82% </text>
									</view>
								</view>
								<ECharts
									canvas-id="disk-chart-info"
									chart-type="line"
									:chart-data="diskChartData"
									:opts="getLoadChartStyle()"
									:height="500"
								/>
							</view>

							<!-- 监控未开启时显示空状态 -->
							<view
								v-else
								class="empty-state-container flex flex-col items-center justify-center text-center"
							>
								<text class="text-28 font-600 color-#333 mb-8">服务器监控未开启</text>
								<text class="text-24 color-#999 mb-24">开启监控后可查看磁盘详细数据</text>
								<uv-button
									type="primary"
									size="mini"
									:custom-style="{ backgroundColor: '#20a50a', borderColor: '#20a50a' }"
									@click="navigateToMonitorPage"
								>
									去开启监控
								</uv-button>
							</view>
						</view>
					</template>
				</statusInfo>

				<statusInfo class="mt-24" title="网络" expanded-text="收起" collapsed-text="详情" @toggle="handleNetworkToggle">
					<template #basic>
						<view class="flex items-center justify-between">
							<view class="flex items-center justify-between w-60%">
								<view class="flex items-center">
									<uv-icon name="arrow-upward" color="#3EAF3B" size="18"></uv-icon>
									<text class="text-24 ml-8 font-800 flex-1">{{ networkIoData.up }}KB/s</text>
								</view>
								<view class="flex items-center">
									<uv-icon name="arrow-downward" color="#EAD928" size="18"></uv-icon>
									<text class="text-24 ml-8 font-800 flex-1">{{ networkIoData.down }}KB/s</text>
								</view>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<!-- 监控已开启时显示图表 -->
							<view v-if="isMonitoringEnabled">
								<ECharts
									canvas-id="network-chart-info"
									chart-type="line"
									:chart-data="newNetworkChartData"
									:opts="getLoadChartStyle()"
									:height="500"
								/>
							</view>

							<!-- 监控未开启时显示空状态 -->
							<view
								v-else
								class="empty-state-container flex flex-col items-center justify-center text-center"
							>
								<text class="text-28 font-600 color-#333 mb-8">服务器监控未开启</text>
								<text class="text-24 color-#999 mb-24">开启监控后可查看网络详细数据</text>
								<uv-button
									type="primary"
									size="mini"
									:custom-style="{ backgroundColor: '#20a50a', borderColor: '#20a50a' }"
									@click="navigateToMonitorPage"
								>
									去开启监控
								</uv-button>
							</view>
						</view>
					</template>
				</statusInfo>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { onReady, onShow, onHide, onUnload } from '@dcloudio/uni-app';
	import CustomNav from '@/components/customNav/index.vue';
	import PageContainer from '@/components/PageContainer/index.vue';
	import ECharts from '@/components/ECharts/index.vue';
	import FunctionList from './functionList.vue';
	import StatusInfo from './statusInfo.vue';
	import MetricProgressBar from '@/components/MetricProgressBar/index.vue';
	import {
		getLoadChartStyle,
		loadChartData,
		cpuChartData,
		getBaseChartConfig,
		chartMap,
		memoryChartData,
		diskChartData,
		newNetworkChartData,
		basicFunctionList,
		pluginFunctionList,
		environmentFunctionList,
		backgroundImage,
		backgroundImageStyle,
		serverBgStyle,
		onPageScrollHandler,
		resetPageScrollPosition,
		handleBasicFunctionClick,
		handlePluginFunctionClick,
		handleEnvironmentFunctionClick,
		handleFunctionListEdit,
		pageContainer,
		serverDisplayInfo,
		serverStatusColor,
		getNetwork,
		startTimer,
		stopTimer,
		initData,
		loadData,
		formatLoadValue,
		// 新增的监控状态相关变量和函数
		isMonitoringEnabled,
		isLoadingMonitorStatus,
		monitorStatusError,
		navigateToMonitorPage,
		// CPU 详细信息
		cpuDetailInfo,
		// CPU 数据（来自 getCpuAndMemoryData API）
		cpuData,
		formatCpuValue,
		// 内存详细信息
		memDetailInfo,
		// 内存数据（来自 getCpuAndMemoryData API）
		memoryData,
		formatMemoryValue,
		formatMemoryPercentage,
		// 网络数据
		networkIoData,
		formatNetworkData,
		handleNetworkToggle,
	} from './useController';

	// 生命周期钩子
	onReady(() => {
		// 初始化图表数据
		initData();
		getNetwork();
	});

	onShow(() => {
		// 重置页面滚动位置到顶部
		resetPageScrollPosition();
		// 设置定时器
		startTimer();
	});

	onHide(() => {
		// 清除定时器
		stopTimer();
	});
</script>
<style lang="scss" scoped>
	.empty-state-container {
		min-height: 300rpx;

		.uv-button {
			transition: all 0.3s ease;

			&:hover {
				transform: translateY(-2rpx);
				box-shadow: 0 4rpx 12rpx rgba(32, 165, 10, 0.3);
			}
		}
	}
</style>
